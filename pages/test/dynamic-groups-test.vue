<template>
	<view class="container">
		<view class="header">
			<text class="title">动态分组系统测试</text>
		</view>
		
		<view class="section">
			<text class="section-title">系统状态</text>
			<view class="status-info">
				<text>应用列表已加载: {{ appListLoaded ? '是' : '否' }}</text>
				<text>权限已加载: {{ permissionsLoaded ? '是' : '否' }}</text>
				<text>动态应用总数: {{ dynamicAppList.length }}</text>
				<text>内部分组数: {{ internalAppGroups.length }}</text>
				<text>可用分组数: {{ availableGroups.length }}</text>
			</view>
		</view>

		<view class="section">
			<text class="section-title">内部应用分组详情</text>
			<view class="groups-detail">
				<view v-for="(group, index) in internalAppGroups" :key="index" class="group-detail">
					<view class="group-header">
						<text class="group-name">{{ group.name }}</text>
						<text class="group-count">({{ group.count }}个应用)</text>
					</view>
					<view class="apps-preview">
						<text v-for="(app, appIndex) in group.apps.slice(0, 3)" :key="appIndex" class="app-preview">
							{{ app.title }}
						</text>
						<text v-if="group.apps.length > 3" class="more-apps">
							...还有{{ group.apps.length - 3 }}个
						</text>
					</view>
				</view>
				<text v-if="internalAppGroups.length === 0" class="empty-text">暂无内部应用分组</text>
			</view>
		</view>

		<view class="section">
			<text class="section-title">可用分组（用于页面显示）</text>
			<view class="available-groups">
				<view v-for="(group, index) in availableGroups" :key="index" class="available-group">
					<view class="group-info">
						<text class="group-name">{{ group.name }}</text>
						<text class="group-type">{{ group.type === 'external' ? '外部应用' : '内部应用' }}</text>
						<text class="group-count">{{ group.count }}个应用</text>
					</view>
					<button @click="testGroupNavigation(group)" class="nav-btn">测试导航</button>
				</view>
				<text v-if="availableGroups.length === 0" class="empty-text">暂无可用分组</text>
			</view>
		</view>

		<view class="section">
			<text class="section-title">分组应用获取测试</text>
			<view class="group-test">
				<input v-model="testGroupName" placeholder="输入分组名称进行测试" class="group-input" />
				<button @click="testGetAppsByGroup" class="test-btn">获取分组应用</button>
				<view v-if="testResult.length > 0" class="test-result">
					<text class="result-title">测试结果 ({{ testResult.length }}个应用):</text>
					<view v-for="(app, index) in testResult" :key="index" class="result-app">
						<text class="app-name">{{ app.title }}</text>
						<text class="app-code">{{ app.name }}</text>
					</view>
				</view>
				<text v-else-if="testGroupName" class="empty-result">未找到该分组或分组为空</text>
			</view>
		</view>

		<view class="section">
			<text class="section-title">权限过滤测试</text>
			<view class="permission-test">
				<text class="test-desc">当前用户权限: {{ userPermissions.length }}个</text>
				<text class="test-desc">原始应用总数: {{ dynamicAppList.length }}个</text>
				<text class="test-desc">有权限的应用: {{ filteredAppsCount }}个</text>
				<text class="test-desc">过滤率: {{ filterRate }}%</text>
			</view>
		</view>

		<view class="actions">
			<button @click="refreshAll" class="btn primary">刷新所有数据</button>
			<button @click="clearCache" class="btn secondary">清除缓存</button>
		</view>
	</view>
</template>

<script>
import appConfig from "/common/appConfig.js"
import { clearAppListCache } from "/pages/user/api/permission.js"

export default {
	mixins: [appConfig],
	data() {
		return {
			testGroupName: '',
			testResult: []
		}
	},
	computed: {
		filteredAppsCount() {
			return this.dynamicAppList.filter(app => this.hasPermission(app.name)).length;
		},
		filterRate() {
			if (this.dynamicAppList.length === 0) return 0;
			return Math.round((this.filteredAppsCount / this.dynamicAppList.length) * 100);
		}
	},
	methods: {
		testGetAppsByGroup() {
			if (!this.testGroupName.trim()) {
				uni.showToast({
					title: '请输入分组名称',
					icon: 'none'
				});
				return;
			}
			
			this.testResult = this.getAppsByGroup(this.testGroupName.trim());
			console.log('分组应用获取测试结果:', this.testResult);
		},
		
		testGroupNavigation(group) {
			console.log('测试分组导航:', group);
			uni.showModal({
				title: '导航测试',
				content: `将导航到分组: ${group.name}\n类型: ${group.type}\n应用数: ${group.count}`,
				showCancel: false
			});
		},
		
		refreshAll() {
			console.log('刷新所有数据');
			Promise.all([
				this.loadAppList(true),
				this.loadUserPermissions(true)
			]).then(() => {
				uni.showToast({
					title: '数据刷新成功',
					icon: 'success'
				});
			}).catch(err => {
				console.error('刷新数据失败:', err);
				uni.showToast({
					title: '刷新失败',
					icon: 'error'
				});
			});
		},
		
		clearCache() {
			console.log('清除缓存');
			clearAppListCache();
			uni.showToast({
				title: '缓存已清除',
				icon: 'success'
			});
		}
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.section {
	background-color: white;
	margin-bottom: 20rpx;
	padding: 20rpx;
	border-radius: 10rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}

.status-info {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.status-info text {
	font-size: 28rpx;
	color: #666;
}

.groups-detail {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.group-detail {
	background-color: #f9f9f9;
	padding: 15rpx;
	border-radius: 8rpx;
	border-left: 4rpx solid #2196f3;
}

.group-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 10rpx;
}

.group-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.group-count {
	font-size: 24rpx;
	color: #666;
}

.apps-preview {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}

.app-preview {
	background-color: #e3f2fd;
	color: #1976d2;
	padding: 4rpx 8rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
}

.more-apps {
	color: #999;
	font-size: 22rpx;
}

.available-groups {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.available-group {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #f9f9f9;
	padding: 15rpx;
	border-radius: 8rpx;
}

.group-info {
	display: flex;
	flex-direction: column;
	gap: 5rpx;
}

.group-type {
	font-size: 24rpx;
	color: #ff9800;
}

.nav-btn {
	background-color: #2196f3;
	color: white;
	border: none;
	padding: 10rpx 20rpx;
	border-radius: 6rpx;
	font-size: 24rpx;
}

.group-test {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.group-input {
	border: 1px solid #ddd;
	padding: 15rpx;
	border-radius: 6rpx;
	font-size: 28rpx;
}

.test-btn {
	background-color: #4caf50;
	color: white;
	border: none;
	padding: 15rpx;
	border-radius: 6rpx;
	font-size: 28rpx;
}

.test-result {
	background-color: #f0f8ff;
	padding: 15rpx;
	border-radius: 6rpx;
}

.result-title {
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
	display: block;
}

.result-app {
	display: flex;
	justify-content: space-between;
	padding: 8rpx 0;
	border-bottom: 1px solid #eee;
}

.permission-test {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.test-desc {
	font-size: 28rpx;
	color: #666;
}

.empty-text, .empty-result {
	color: #999;
	font-size: 26rpx;
	text-align: center;
	padding: 20rpx;
}

.actions {
	display: flex;
	gap: 20rpx;
	margin-top: 30rpx;
}

.btn {
	flex: 1;
	border: none;
	padding: 20rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
}

.primary {
	background-color: #2196f3;
	color: white;
}

.secondary {
	background-color: #ff9800;
	color: white;
}
</style>
