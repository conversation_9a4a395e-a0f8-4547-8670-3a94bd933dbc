<template>
	<view class="container">
		<view class="header">
			<text class="title">动态应用列表测试</text>
		</view>

		<view class="section">
			<text class="section-title">应用列表加载状态</text>
			<view class="status-info">
				<text>应用列表已加载: {{ appListLoaded ? '是' : '否' }}</text>
				<text>权限已加载: {{ permissionsLoaded ? '是' : '否' }}</text>
				<text>应用总数: {{ dynamicAppList.length }}</text>
				<text v-if="appListError">错误信息: {{ appListError }}</text>
			</view>
		</view>

		<view class="section">
			<text class="section-title">用户权限列表</text>
			<view class="permissions-list">
				<text v-for="(permission, index) in userPermissions" :key="index" class="permission-item">
					{{ permission }}
				</text>
				<text v-if="userPermissions.length === 0" class="empty-text">暂无权限</text>
			</view>
		</view>

		<view class="section">
			<text class="section-title">告警管理应用 ({{ assetManageApps.length }})</text>
			<view class="app-list">
				<view v-for="(app, index) in assetManageApps" :key="index" class="app-item">
					<text class="app-name">{{ app.title }}</text>
					<text class="app-code">{{ app.name }}</text>
					<text class="app-group">{{ app.group }}</text>
				</view>
				<text v-if="assetManageApps.length === 0" class="empty-text">暂无应用</text>
			</view>
		</view>

		<view class="section">
			<text class="section-title">工单管理应用 ({{ listManageApps.length }})</text>
			<view class="app-list">
				<view v-for="(app, index) in listManageApps" :key="index" class="app-item">
					<text class="app-name">{{ app.title }}</text>
					<text class="app-code">{{ app.name }}</text>
					<text class="app-group">{{ app.group }}</text>
				</view>
				<text v-if="listManageApps.length === 0" class="empty-text">暂无应用</text>
			</view>
		</view>

		<view class="section">
			<text class="section-title">态势呈现应用 ({{ messageManageApps.length }})</text>
			<view class="app-list">
				<view v-for="(app, index) in messageManageApps" :key="index" class="app-item">
					<text class="app-name">{{ app.title }}</text>
					<text class="app-code">{{ app.name }}</text>
					<text class="app-group">{{ app.group }}</text>
				</view>
				<text v-if="messageManageApps.length === 0" class="empty-text">暂无应用</text>
			</view>
		</view>

		<view class="section">
			<text class="section-title">动态分组列表</text>
			<view class="groups-list">
				<view v-for="(group, index) in availableGroups" :key="index" class="group-item">
					<text class="group-name">{{ group.name }} ({{ group.count }})</text>
					<text class="group-type">类型: {{ group.type }}</text>
					<view class="group-apps">
						<view v-for="(app, appIndex) in group.apps" :key="appIndex" class="app-item" :class="{ 'external-app': group.type === 'external' }">
							<text class="app-name">{{ app.title }}</text>
							<text class="app-code">{{ app.name }}</text>
							<text class="app-group">{{ app.group }}</text>
							<text class="app-type">{{ app.type }}</text>
							<text class="app-url">{{ app.url }}</text>
							<button @click="handleAppClick(app)" class="test-btn">测试点击</button>
						</view>
					</view>
				</view>
				<text v-if="availableGroups.length === 0" class="empty-text">暂无分组</text>
			</view>
		</view>

		<view class="section">
			<text class="section-title">外部应用 ({{ externalApps.length }})</text>
			<view class="app-list">
				<view v-for="(app, index) in externalApps" :key="index" class="app-item external-app">
					<text class="app-name">{{ app.title }}</text>
					<text class="app-code">{{ app.name }}</text>
					<text class="app-group">{{ app.group }}</text>
					<text class="app-type">{{ app.type }}</text>
					<text class="app-url">{{ app.url }}</text>
					<button @click="handleAppClick(app)" class="test-btn">测试点击</button>
				</view>
				<text v-if="externalApps.length === 0" class="empty-text">暂无外部应用</text>
			</view>
		</view>

		<view class="section">
			<text class="section-title">所有动态应用</text>
			<view class="app-list">
				<view v-for="(app, index) in dynamicAppList" :key="index" class="app-item">
					<text class="app-name">{{ app.title }}</text>
					<text class="app-code">{{ app.name }}</text>
					<text class="app-group">{{ app.group }}</text>
					<text class="app-url">{{ app.url }}</text>
				</view>
				<text v-if="dynamicAppList.length === 0" class="empty-text">暂无应用</text>
			</view>
		</view>

		<view class="actions">
			<button @click="refreshAppList" class="btn">刷新应用列表</button>
			<button @click="refreshPermissions" class="btn">刷新权限</button>
		</view>
	</view>
</template>

<script>
import appConfig from "/common/appConfig.js"

export default {
	mixins: [appConfig],
	data() {
		return {
			// 继承自appConfig的数据
		}
	},
	methods: {
		refreshAppList() {
			console.log('手动刷新应用列表');
			this.loadAppList(true).then(() => {
				uni.showToast({
					title: '应用列表刷新成功',
					icon: 'success'
				});
			}).catch(err => {
				console.error('刷新应用列表失败:', err);
				uni.showToast({
					title: '刷新失败',
					icon: 'error'
				});
			});
		},

		refreshPermissions() {
			console.log('手动刷新权限');
			this.loadUserPermissions(true).then(() => {
				uni.showToast({
					title: '权限刷新成功',
					icon: 'success'
				});
			}).catch(err => {
				console.error('刷新权限失败:', err);
				uni.showToast({
					title: '刷新失败',
					icon: 'error'
				});
			});
		}
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.section {
	background-color: white;
	margin-bottom: 20rpx;
	padding: 20rpx;
	border-radius: 10rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}

.status-info {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.status-info text {
	font-size: 28rpx;
	color: #666;
}

.permissions-list {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
}

.permission-item {
	background-color: #e3f2fd;
	color: #1976d2;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.app-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.app-item {
	background-color: #f9f9f9;
	padding: 15rpx;
	border-radius: 8rpx;
	border-left: 4rpx solid #2196f3;
}

.external-app {
	border-left-color: #ff9800;
	background-color: #fff3e0;
}

.app-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.app-code {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.app-group {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 5rpx;
}

.app-url {
	font-size: 22rpx;
	color: #999;
	display: block;
	word-break: break-all;
}

.empty-text {
	color: #999;
	font-size: 26rpx;
	text-align: center;
	padding: 20rpx;
}

.actions {
	display: flex;
	gap: 20rpx;
	margin-top: 30rpx;
}

.btn {
	flex: 1;
	background-color: #2196f3;
	color: white;
	border: none;
	padding: 20rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
}

.btn:active {
	background-color: #1976d2;
}

.test-btn {
	background-color: #ff9800;
	color: white;
	border: none;
	padding: 10rpx 20rpx;
	border-radius: 4rpx;
	font-size: 24rpx;
	margin-top: 10rpx;
}

.test-btn:active {
	background-color: #f57c00;
}

.app-type {
	font-size: 22rpx;
	color: #ff9800;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.groups-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.group-item {
	background-color: #f0f8ff;
	padding: 20rpx;
	border-radius: 10rpx;
	border-left: 6rpx solid #2196f3;
}

.group-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #2196f3;
	display: block;
	margin-bottom: 10rpx;
}

.group-type {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.group-apps {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}
</style>
