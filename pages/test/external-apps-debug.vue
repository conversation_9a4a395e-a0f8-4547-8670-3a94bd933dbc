<template>
	<view class="container">
		<view class="header">
			<text class="title">外部应用调试页面</text>
		</view>

		<!-- 数据状态 -->
		<view class="section">
			<text class="section-title">数据加载状态</text>
			<view class="status-list">
				<text class="status-item">应用列表已加载: {{ appListLoaded ? '是' : '否' }}</text>
				<text class="status-item">权限已加载: {{ permissionsLoaded ? '是' : '否' }}</text>
				<text class="status-item">动态应用总数: {{ dynamicAppList.length }}</text>
				<text class="status-item">用户权限数: {{ userPermissions.length }}</text>
			</view>
		</view>

		<!-- 原始数据 -->
		<view class="section">
			<text class="section-title">原始外部应用数据</text>
			<view class="data-list">
				<view v-for="(app, index) in rawExternalApps" :key="index" class="data-item">
					<text class="app-title">{{ app.title }} ({{ app.name }})</text>
					<text class="app-details">类型: {{ app.type }}</text>
					<text class="app-details">分组: {{ app.group }}</text>
					<text class="app-details">URL: {{ app.url }}</text>
					<text class="app-details">图标: {{ app.image || '无' }}</text>
				</view>
				<text v-if="rawExternalApps.length === 0" class="empty-text">没有找到外部应用</text>
			</view>
		</view>

		<!-- 权限过滤后的数据 -->
		<view class="section">
			<text class="section-title">权限过滤后的外部应用</text>
			<view class="data-list">
				<view v-for="(app, index) in externalApps" :key="index" class="data-item filtered">
					<text class="app-title">{{ app.title }} ({{ app.name }})</text>
					<text class="app-details">权限状态: {{ hasPermission(app.name) ? '有权限' : '无权限' }}</text>
					<text class="app-details">类型: {{ app.type }}</text>
					<text class="app-details">分组: {{ app.group }}</text>
					<button @click="testAppClick(app)" class="test-btn">测试点击</button>
				</view>
				<text v-if="externalApps.length === 0" class="empty-text">权限过滤后没有外部应用</text>
			</view>
		</view>

		<!-- 用户权限列表 -->
		<view class="section">
			<text class="section-title">用户权限列表</text>
			<view class="permissions-list">
				<text v-for="(permission, index) in userPermissions" :key="index" class="permission-item">
					{{ permission }}
				</text>
				<text v-if="userPermissions.length === 0" class="empty-text">没有权限数据</text>
			</view>
		</view>

		<!-- 权限匹配分析 -->
		<view class="section">
			<text class="section-title">权限匹配分析</text>
			<view class="analysis-list">
				<view v-for="(app, index) in rawExternalApps" :key="index" class="analysis-item">
					<text class="app-name">{{ app.title }} ({{ app.name }})</text>
					<text class="match-result" :class="{ 'has-permission': hasPermission(app.name), 'no-permission': !hasPermission(app.name) }">
						{{ hasPermission(app.name) ? '✅ 有权限' : '❌ 无权限' }}
					</text>
				</view>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="actions">
			<button @click="refreshData" class="btn primary">刷新数据</button>
			<button @click="logDebugInfo" class="btn secondary">输出调试信息</button>
		</view>
	</view>
</template>

<script>
import appConfig from "/common/appConfig.js"

export default {
	mixins: [appConfig],
	computed: {
		// 获取原始的外部应用数据（未过滤权限）
		rawExternalApps() {
			if (!this.appListLoaded) {
				return [];
			}
			return this.dynamicAppList.filter(app => app.type === '外部应用');
		}
	},
	mounted() {
		console.log('外部应用调试页面加载');
		this.logDebugInfo();
	},
	methods: {
		testAppClick(app) {
			console.log('测试外部应用点击:', app);
			this.handleAppClick(app);
		},

		refreshData() {
			console.log('刷新数据...');
			Promise.all([
				this.loadAppList(true),
				this.loadUserPermissions(true)
			]).then(() => {
				uni.showToast({
					title: '数据刷新成功',
					icon: 'success'
				});
				this.logDebugInfo();
			}).catch(err => {
				console.error('刷新数据失败:', err);
				uni.showToast({
					title: '刷新失败',
					icon: 'error'
				});
			});
		},

		logDebugInfo() {
			console.log('=== 外部应用调试信息 ===');
			console.log('应用列表已加载:', this.appListLoaded);
			console.log('权限已加载:', this.permissionsLoaded);
			console.log('动态应用列表总数:', this.dynamicAppList.length);
			console.log('动态应用列表:', this.dynamicAppList);
			console.log('原始外部应用数据:', this.rawExternalApps);
			console.log('用户权限列表:', this.userPermissions);
			console.log('过滤后的外部应用:', this.externalApps);
			
			// 分析每个外部应用的权限状态
			this.rawExternalApps.forEach(app => {
				const hasPermission = this.hasPermission(app.name);
				console.log(`外部应用 ${app.title} (${app.name}) 权限状态:`, hasPermission);
			});
			
			console.log('=== 调试信息结束 ===');
		}
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.section {
	background-color: white;
	margin-bottom: 20rpx;
	padding: 20rpx;
	border-radius: 10rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}

.status-list, .data-list, .permissions-list, .analysis-list {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.status-item {
	font-size: 28rpx;
	color: #666;
	padding: 8rpx;
	background-color: #f9f9f9;
	border-radius: 4rpx;
}

.data-item {
	padding: 15rpx;
	background-color: #f9f9f9;
	border-radius: 8rpx;
	border-left: 4rpx solid #2196f3;
}

.data-item.filtered {
	border-left-color: #4caf50;
}

.app-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.app-details {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 5rpx;
	display: block;
}

.test-btn {
	background-color: #ff9800;
	color: white;
	border: none;
	padding: 10rpx 20rpx;
	border-radius: 6rpx;
	font-size: 24rpx;
	margin-top: 10rpx;
}

.permission-item {
	font-size: 26rpx;
	color: #333;
	padding: 8rpx;
	background-color: #e3f2fd;
	border-radius: 4rpx;
}

.analysis-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx;
	background-color: #f9f9f9;
	border-radius: 8rpx;
}

.app-name {
	font-weight: bold;
	color: #333;
}

.match-result {
	font-size: 24rpx;
}

.match-result.has-permission {
	color: #4caf50;
}

.match-result.no-permission {
	color: #f44336;
}

.empty-text {
	text-align: center;
	color: #999;
	font-size: 26rpx;
	padding: 20rpx;
}

.actions {
	display: flex;
	gap: 20rpx;
	margin-top: 30rpx;
}

.btn {
	flex: 1;
	border: none;
	padding: 20rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
}

.primary {
	background-color: #2196f3;
	color: white;
}

.secondary {
	background-color: #ff9800;
	color: white;
}
</style>
