<template>
	<view class="container">
		<view class="header">
			<text class="title">动态分组系统综合测试</text>
		</view>
		
		<!-- 测试结果汇总 -->
		<view class="section">
			<text class="section-title">测试结果汇总</text>
			<view class="test-summary">
				<view v-for="(result, index) in testResults" :key="index" class="test-item" :class="{ 'success': result.passed, 'failed': !result.passed }">
					<text class="test-name">{{ result.name }}</text>
					<text class="test-status">{{ result.passed ? '✅ 通过' : '❌ 失败' }}</text>
					<text v-if="result.message" class="test-message">{{ result.message }}</text>
				</view>
			</view>
		</view>

		<!-- 详细测试信息 -->
		<view class="section">
			<text class="section-title">系统状态</text>
			<view class="status-grid">
				<view class="status-item">
					<text class="status-label">应用列表加载</text>
					<text class="status-value">{{ appListLoaded ? '✅' : '❌' }}</text>
				</view>
				<view class="status-item">
					<text class="status-label">权限加载</text>
					<text class="status-value">{{ permissionsLoaded ? '✅' : '❌' }}</text>
				</view>
				<view class="status-item">
					<text class="status-label">动态应用数</text>
					<text class="status-value">{{ dynamicAppList.length }}</text>
				</view>
				<view class="status-item">
					<text class="status-label">内部分组数</text>
					<text class="status-value">{{ internalAppGroups.length }}</text>
				</view>
				<view class="status-item">
					<text class="status-label">可用分组数</text>
					<text class="status-value">{{ availableGroups.length }}</text>
				</view>
				<view class="status-item">
					<text class="status-label">外部应用数</text>
					<text class="status-value">{{ externalApps.length }}</text>
				</view>
			</view>
		</view>

		<!-- 分组详情 -->
		<view class="section">
			<text class="section-title">分组详情</text>
			<view class="groups-detail">
				<view v-for="(group, index) in availableGroups" :key="index" class="group-card">
					<view class="group-header">
						<text class="group-name">{{ group.name }}</text>
						<text class="group-type">{{ group.type === 'external' ? '外部' : '内部' }}</text>
						<text class="group-count">{{ group.count }}个</text>
					</view>
					<view class="group-apps">
						<text v-for="(app, appIndex) in group.apps.slice(0, 3)" :key="appIndex" class="app-tag">
							{{ app.title }}
						</text>
						<text v-if="group.apps.length > 3" class="more-tag">+{{ group.apps.length - 3 }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 测试操作 -->
		<view class="section">
			<text class="section-title">测试操作</text>
			<view class="test-actions">
				<button @click="runAllTests" class="btn primary">运行所有测试</button>
				<button @click="testBoundaryConditions" class="btn secondary">边界条件测试</button>
				<button @click="testPerformance" class="btn secondary">性能测试</button>
				<button @click="clearAllCache" class="btn danger">清除所有缓存</button>
			</view>
		</view>

		<!-- 测试日志 -->
		<view class="section">
			<text class="section-title">测试日志</text>
			<scroll-view class="test-log" scroll-y>
				<text v-for="(log, index) in testLogs" :key="index" class="log-item">
					{{ log }}
				</text>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import appConfig from "/common/appConfig.js"
import { clearAppListCache } from "/pages/user/api/permission.js"

export default {
	mixins: [appConfig],
	data() {
		return {
			testResults: [],
			testLogs: []
		}
	},
	mounted() {
		this.runAllTests();
	},
	methods: {
		log(message) {
			const timestamp = new Date().toLocaleTimeString();
			this.testLogs.unshift(`[${timestamp}] ${message}`);
			console.log(message);
		},

		addTestResult(name, passed, message = '') {
			this.testResults.push({ name, passed, message });
		},

		async runAllTests() {
			this.testResults = [];
			this.testLogs = [];
			this.log('开始运行综合测试...');

			// 等待数据加载完成
			await this.waitForDataLoaded();

			// 1. 动态分组核心功能测试
			this.testDynamicGrouping();

			// 2. 权限集成测试
			this.testPermissionIntegration();

			// 3. 外部应用处理测试
			this.testExternalApps();

			// 4. 向后兼容性测试
			this.testBackwardCompatibility();

			// 5. 边界条件测试
			this.testBoundaryConditions();

			this.log('所有测试完成');
		},

		async waitForDataLoaded() {
			let attempts = 0;
			while ((!this.appListLoaded || !this.permissionsLoaded) && attempts < 30) {
				await new Promise(resolve => setTimeout(resolve, 1000));
				attempts++;
			}
			this.log(`数据加载等待完成，尝试次数: ${attempts}`);
		},

		testDynamicGrouping() {
			this.log('测试动态分组功能...');

			// 测试 internalAppGroups 计算属性
			const hasInternalGroups = Array.isArray(this.internalAppGroups);
			this.addTestResult('内部分组数组格式', hasInternalGroups);

			// 测试分组结构
			const groupStructureValid = this.internalAppGroups.every(group => 
				group.name && Array.isArray(group.apps) && typeof group.count === 'number'
			);
			this.addTestResult('分组结构有效性', groupStructureValid);

			// 测试 availableGroups
			const hasAvailableGroups = Array.isArray(this.availableGroups);
			this.addTestResult('可用分组数组格式', hasAvailableGroups);

			// 测试 getAppsByGroup 方法
			if (this.availableGroups.length > 0) {
				const firstGroup = this.availableGroups[0];
				const groupApps = this.getAppsByGroup(firstGroup.name);
				const methodWorks = Array.isArray(groupApps);
				this.addTestResult('getAppsByGroup方法', methodWorks);
			}
		},

		testPermissionIntegration() {
			this.log('测试权限集成...');

			// 测试权限过滤
			const totalApps = this.dynamicAppList.length;
			const filteredApps = this.dynamicAppList.filter(app => this.hasPermission(app.name)).length;
			const hasPermissionFiltering = filteredApps <= totalApps;
			this.addTestResult('权限过滤功能', hasPermissionFiltering, `${filteredApps}/${totalApps} 应用有权限`);

			// 测试分组权限过滤
			const allGroupApps = this.internalAppGroups.reduce((total, group) => total + group.count, 0);
			const groupFilteringWorks = allGroupApps <= filteredApps;
			this.addTestResult('分组权限过滤', groupFilteringWorks);
		},

		testExternalApps() {
			this.log('测试外部应用处理...');

			// 测试外部应用分类
			const externalApps = this.dynamicAppList.filter(app => app.type === '外部应用');
			const externalAppsFiltered = this.externalApps;
			const externalClassificationWorks = externalAppsFiltered.length <= externalApps.length;
			this.addTestResult('外部应用分类', externalClassificationWorks);

			// 测试外部应用在可用分组中
			const hasExternalGroup = this.availableGroups.some(group => group.type === 'external');
			const shouldHaveExternal = this.externalApps.length > 0;
			this.addTestResult('外部应用分组显示', hasExternalGroup === shouldHaveExternal);
		},

		testBackwardCompatibility() {
			this.log('测试向后兼容性...');

			// 测试原有计算属性
			const assetAppsWorks = Array.isArray(this.assetManageApps);
			const listAppsWorks = Array.isArray(this.listManageApps);
			const messageAppsWorks = Array.isArray(this.messageManageApps);

			this.addTestResult('告警管理应用兼容性', assetAppsWorks);
			this.addTestResult('工单管理应用兼容性', listAppsWorks);
			this.addTestResult('态势呈现应用兼容性', messageAppsWorks);
		},

		testBoundaryConditions() {
			this.log('测试边界条件...');

			// 测试空分组名称
			const emptyGroupApps = this.getAppsByGroup('');
			const emptyNameHandled = Array.isArray(emptyGroupApps) && emptyGroupApps.length === 0;
			this.addTestResult('空分组名称处理', emptyNameHandled);

			// 测试无效分组名称
			const invalidGroupApps = this.getAppsByGroup(null);
			const invalidNameHandled = Array.isArray(invalidGroupApps) && invalidGroupApps.length === 0;
			this.addTestResult('无效分组名称处理', invalidNameHandled);

			// 测试不存在的分组
			const nonExistentApps = this.getAppsByGroup('不存在的分组');
			const nonExistentHandled = Array.isArray(nonExistentApps) && nonExistentApps.length === 0;
			this.addTestResult('不存在分组处理', nonExistentHandled);
		},

		testPerformance() {
			this.log('测试性能...');

			const start = performance.now();
			
			// 多次调用计算属性
			for (let i = 0; i < 100; i++) {
				const _ = this.availableGroups;
				const __ = this.internalAppGroups;
			}
			
			const end = performance.now();
			const duration = end - start;
			
			const performanceGood = duration < 100; // 100ms内完成
			this.addTestResult('性能测试', performanceGood, `${duration.toFixed(2)}ms`);
		},

		clearAllCache() {
			this.log('清除所有缓存...');
			clearAppListCache();
			// 这里可以添加清除其他缓存的逻辑
			uni.showToast({
				title: '缓存已清除',
				icon: 'success'
			});
		}
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.section {
	background-color: white;
	margin-bottom: 20rpx;
	padding: 20rpx;
	border-radius: 10rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}

.test-summary {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.test-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx;
	border-radius: 8rpx;
	background-color: #f9f9f9;
}

.test-item.success {
	background-color: #e8f5e8;
	border-left: 4rpx solid #4caf50;
}

.test-item.failed {
	background-color: #ffeaea;
	border-left: 4rpx solid #f44336;
}

.test-name {
	font-weight: bold;
	color: #333;
}

.test-status {
	font-size: 24rpx;
}

.test-message {
	font-size: 22rpx;
	color: #666;
}

.status-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 15rpx;
}

.status-item {
	display: flex;
	justify-content: space-between;
	padding: 10rpx;
	background-color: #f9f9f9;
	border-radius: 6rpx;
}

.status-label {
	font-size: 26rpx;
	color: #666;
}

.status-value {
	font-weight: bold;
	color: #333;
}

.groups-detail {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.group-card {
	background-color: #f9f9f9;
	padding: 15rpx;
	border-radius: 8rpx;
	border-left: 4rpx solid #2196f3;
}

.group-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}

.group-name {
	font-weight: bold;
	color: #333;
}

.group-type {
	background-color: #2196f3;
	color: white;
	padding: 4rpx 8rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
}

.group-count {
	color: #666;
	font-size: 24rpx;
}

.group-apps {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}

.app-tag {
	background-color: #e3f2fd;
	color: #1976d2;
	padding: 4rpx 8rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
}

.more-tag {
	background-color: #f5f5f5;
	color: #999;
	padding: 4rpx 8rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
}

.test-actions {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 15rpx;
}

.btn {
	padding: 20rpx;
	border: none;
	border-radius: 8rpx;
	font-size: 28rpx;
	text-align: center;
}

.primary {
	background-color: #2196f3;
	color: white;
}

.secondary {
	background-color: #ff9800;
	color: white;
}

.danger {
	background-color: #f44336;
	color: white;
}

.test-log {
	height: 300rpx;
	background-color: #f9f9f9;
	padding: 15rpx;
	border-radius: 8rpx;
}

.log-item {
	display: block;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
	font-family: monospace;
}
</style>
