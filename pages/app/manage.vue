<template>
	<view class="container" :class="{ 'container-dark': theme }">
		<!-- <view style="width: 100%;background-color: #fff;padding: 16rpx 26rpx 6rpx 26rpx;">
			<uni-easyinput style="width: 13rem;" class="uni-easyinput-input" suffixIcon="search" v-model="code"
				placeholder="应用名称"></uni-easyinput>
		</view> -->
		<!-- Tab 标签栏 -->
		<scroll-view ref="tabScroll" :show-scrollbar="false" scroll-x class="tab-scroll" scroll-with-animation
			:scroll-left="scrollLeft">
			<view class="tab-bar">
				<!-- :class="{ 'active': activeIndex == index }" -->
				<view :ref="`tabItem${index}`" v-for="(item, index) in tabs" :key="index" class="tab-item"
					@click="switchTab(index)">
					{{ item.title }}
				</view>
				<!-- 底部滑动条 -->
				<view ref="tabLine" class="tab-line" :style="lineStyle"></view>
			</view>
		</scroll-view>

		<!-- 内容区域 -->
		<swiper :current="activeIndex" @change="onSwiperChange" :duration="300">
			<swiper-item v-for="(item, index) in tabs" :key="index">
				<view class="content" style="padding-bottom: 26rpx;">
					<scroll-view class="app-manage">
						<uni-section v-if="showCommonApps && (item.title == '全部应用')" title="常用服务" class="page-group"
							:border="false">
							<template #right>
								<view style="display: flex; align-items: center;color: #3190F9;">
									<view style="font-size: 0.8em;margin-right: 10px;" @click="resetAppList">重置</view>
									<view v-if="!editing" style="font-size: 0.8em;" @click="editing = true">编辑</view>
									<view v-else style="font-size: 0.8em;" @click="editing = false">保存</view>
								</view>
							</template>
							<uni-grid :showBorder="false" :column="3" :highlight="true">
								<uni-grid-item v-for="(item, itemIndex) in commonApps" :index="itemIndex"
									:key="itemIndex" style="height: 150rpx;">
									<!-- {{ item.title }} -->
									<navigator v-if="item.title != '创建工单'" :url="!editing && item.url"
										open-type="navigate" style="text-align: center;"
										hover-class="other-navigator-hover">
										<view class="grid-item-box">
											<view
												style="position: relative;width: 36px; height: 26px;display: flex; align-items: center; justify-content: center;">
												<image :src="item.image" style="width: 26px; height: 26px;"></image>
												<uni-icons v-if="editing" class="tool-icon" type="minus-filled"
													style="position: absolute;color: gray; opacity: .8; float: right;"
													@click="removeApp(itemIndex)"></uni-icons>
											</view>
											<text class="text">{{ item.title }}</text>
										</view>
									</navigator>
									<view v-if="item.title == '创建工单'" :url="!editing && item.url" open-type="navigate"
										style="text-align: center;" hover-class="other-navigator-hover">
										<view class="grid-item-box">
											<view
												style="position: relative;width: 36px; height: 26px;display: flex; align-items: center; justify-content: center;">
												<image @click="showPop(item.url)" :src="item.image"
													style="width: 26px; height: 26px;"></image>
												<uni-icons v-if="editing" class="tool-icon" type="minus-filled"
													style="position: absolute;color: gray; opacity: .8; float: right;"
													@click="removeApp(itemIndex)"></uni-icons>
											</view>
											<text @click="showPop(item.url)" class="text">{{ item.title }}</text>
										</view>
									</view>
								</uni-grid-item>
							</uni-grid>
						</uni-section>
						<uni-section v-if="showListManageCategory && showListApps && (item.title == '全部应用' || item.title == '工单管理')" title="工单管理"
							class="page-group" :border="false">
							<uni-grid :showBorder="false" :column="3" :highlight="true">
								<uni-grid-item v-for="(item, itemIndex) in listManageApps" :index="itemIndex"
									:key="itemIndex" style="height: 150rpx;">
									<navigator v-if="item.title != '创建工单'" :url="!editing && item.url"
										open-type="navigate" style="text-align: center;"
										hover-class="other-navigator-hover">
										<view class="grid-item-box">
											<view
												style="position: relative;width: 36px; height: 26px;display: flex; align-items: center; justify-content: center;">
												<image :src="item.image" style="width: 26px; height: 26px;"></image>
												<uni-icons v-if="editing && !commonAppNames.includes(item.name)"
													class="tool-icon" type="plus-filled" :style="addAppIconStyle"
													style="position: absolute;float: right;"
													@click="addApp(item)"></uni-icons>
											</view>
											<text class="text">{{ item.title }}</text>
										</view>
									</navigator>
									<view v-if="item.title == '创建工单'" :url="!editing && item.url" open-type="navigate"
										style="text-align: center;" hover-class="other-navigator-hover">
										<view class="grid-item-box">
											<view
												style="position: relative;width: 36px; height: 26px;display: flex; align-items: center; justify-content: center;">
												<image @click="showPop(item.url)" :src="item.image"
													style="width: 26px; height: 26px;"></image>
												<uni-icons v-if="editing && !commonAppNames.includes(item.name)"
													class="tool-icon" type="plus-filled" :style="addAppIconStyle"
													style="position: absolute;float: right;"
													@click="addApp(item)"></uni-icons>
											</view>
											<text @click="showPop(item.url)" class="text">{{ item.title }}</text>
										</view>
									</view>

								</uni-grid-item>
							</uni-grid>
						</uni-section>
						<uni-section v-if="showAssetManageCategory && showAssetApps && (item.title == '全部应用' || item.title == '告警管理')" title="告警管理"
							class="page-group" :border="false">
							<uni-grid :showBorder="false" :column="3" :highlight="true">
								<uni-grid-item v-for="(item, itemIndex) in assetManageApps" :index="itemIndex"
									:key="itemIndex" style="height: 150rpx;">
									<navigator :url="!editing && item.url" open-type="navigate"
										style="text-align: center;" hover-class="other-navigator-hover">
										<view class="grid-item-box">
											<view
												style="position: relative;width: 36px; height: 26px;display: flex; align-items: center; justify-content: center;">
												<image :src="item.image" style="width: 26px; height: 26px;"></image>
												<uni-icons v-if="editing && !commonAppNames.includes(item.name)"
													class="tool-icon" type="plus-filled" :style="addAppIconStyle"
													style="position: absolute;;float: right;"
													@click="addApp(item)"></uni-icons>
											</view>
											<text class="text">{{ item.title }}</text>
										</view>
									</navigator>

								</uni-grid-item>
							</uni-grid>
						</uni-section>

						<uni-section v-if="showMessageManageCategory && showMessageApps && (item.title == '全部应用' || item.title == '态势呈现')" title="态势呈现"
							class="page-group" :border="false">
							<uni-grid :showBorder="false" :column="3" :highlight="true">
								<uni-grid-item v-for="(item, itemIndex) in messageManageApps" :index="itemIndex"
									:key="itemIndex" style="height: 150rpx;">
									<navigator :url="!editing && item.url" open-type="navigate"
										style="text-align: center;" hover-class="other-navigator-hover">
										<view class="grid-item-box">
											<view
												style="position: relative;width: 36px; height: 26px;display: flex; align-items: center; justify-content: center;">
												<image :src="item.image" style="width: 26px; height: 26px;"></image>
												<uni-icons v-if="editing && !commonAppNames.includes(item.name)"
													class="tool-icon" type="plus-filled" :style="addAppIconStyle"
													style="position: absolute;float: right;"
													@click="addApp(item)"></uni-icons>
											</view>
											<text class="text">{{ item.title }}</text>
										</view>
									</navigator>

								</uni-grid-item>
							</uni-grid>
						</uni-section>

						<uni-section v-if="showExternalAppsCategory && showExternalApps && (item.title == '全部应用' || item.title == '外部应用')" title="外部应用"
							class="page-group" :border="false">
							<uni-grid :showBorder="false" :column="3" :highlight="true">
								<uni-grid-item v-for="(item, itemIndex) in externalApps" :index="itemIndex"
									:key="itemIndex" style="height: 150rpx;">
									<view @click="!editing && handleAppClick(item)"
										style="text-align: center;" hover-class="other-navigator-hover">
										<view class="grid-item-box">
											<view
												style="position: relative;width: 36px; height: 26px;display: flex; align-items: center; justify-content: center;">
												<image :src="item.image" style="width: 26px; height: 26px;"></image>
												<uni-icons v-if="editing && !commonAppNames.includes(item.name)"
													class="tool-icon" type="plus-filled" :style="addAppIconStyle"
													style="position: absolute;float: right;"
													@click="addApp(item)"></uni-icons>
											</view>
											<text class="text">{{ item.title }}</text>
											<text class="external-label">外部</text>
										</view>
									</view>

								</uni-grid-item>
							</uni-grid>
						</uni-section>

						<!-- 动态分组显示 -->
						<template v-for="(group, groupIndex) in availableGroups" :key="groupIndex">
							<uni-section
								v-if="(item.title == '全部应用' || item.title == group.name) && group.count > 0"
								:title="group.name"
								class="page-group"
								:border="false">
								<uni-grid :showBorder="false" :column="3" :highlight="true">
									<uni-grid-item v-for="(app, appIndex) in group.apps" :index="appIndex"
										:key="appIndex" style="height: 150rpx;">
										<!-- 外部应用特殊处理 -->
										<view v-if="group.type === 'external'" @click="!editing && handleAppClick(app)"
											style="text-align: center;" hover-class="other-navigator-hover">
											<view class="grid-item-box">
												<view
													style="position: relative;width: 36px; height: 26px;display: flex; align-items: center; justify-content: center;">
													<image :src="app.image" style="width: 26px; height: 26px;"></image>
													<uni-icons v-if="editing && !commonAppNames.includes(app.name)"
														class="tool-icon" type="plus-filled" :style="addAppIconStyle"
														style="position: absolute;float: right;"
														@click="addApp(app)"></uni-icons>
												</view>
												<text class="text">{{ app.title }}</text>
												<text class="external-label">外部</text>
											</view>
										</view>
										<!-- 内部应用处理 -->
										<template v-else>
											<navigator v-if="app.title != '创建工单'" :url="!editing && app.url"
												open-type="navigate" style="text-align: center;"
												hover-class="other-navigator-hover">
												<view class="grid-item-box">
													<view
														style="position: relative;width: 36px; height: 26px;display: flex; align-items: center; justify-content: center;">
														<image :src="app.image" style="width: 26px; height: 26px;"></image>
														<uni-icons v-if="editing && !commonAppNames.includes(app.name)"
															class="tool-icon" type="plus-filled" :style="addAppIconStyle"
															style="position: absolute;float: right;"
															@click="addApp(app)"></uni-icons>
													</view>
													<text class="text">{{ app.title }}</text>
												</view>
											</navigator>
											<view v-if="app.title == '创建工单'" style="text-align: center;" hover-class="other-navigator-hover">
												<view class="grid-item-box">
													<view
														style="position: relative;width: 36px; height: 26px;display: flex; align-items: center; justify-content: center;">
														<image @click="showPop(app.url)" :src="app.image"
															style="width: 26px; height: 26px;"></image>
														<uni-icons v-if="editing && !commonAppNames.includes(app.name)"
															class="tool-icon" type="plus-filled" :style="addAppIconStyle"
															style="position: absolute;float: right;"
															@click="addApp(app)"></uni-icons>
													</view>
													<text @click="showPop(app.url)" class="text">{{ app.title }}</text>
												</view>
											</view>
										</template>
									</uni-grid-item>
								</uni-grid>
							</uni-section>
						</template>
					</scroll-view>
				</view>
			</swiper-item>

		</swiper>


		<!-- 弹窗设计 -->
		<uni-popup style="z-index: 1000 !important;" class="popup" ref="popup" background-color="#fff">
			<uni-section title="选择流程">
				<template #right>
					<uni-icons type="close" @click="closePop"></uni-icons>
				</template>
				<scroll-view class="popup-content" scroll-y>
					<uni-row style="height: calc(100% - 1px); overflow: auto;">
						<uni-col :span="24" v-for="(procItem, itemIndex) in procSelects" :index="itemIndex"
							:key="itemIndex">
							<view style="padding: 5px 10px;">
								<!-- :class="{'process-type-selected': selectProcKey == procItem.procKey}" -->
								<view class="process-type" @click="createToDolist(procItem)">
									<text :title="procItem.label">{{ procItem.label }}</text>
								</view>
							</view>
						</uni-col>
					</uni-row>
				</scroll-view>
			</uni-section>
			<!-- <button type="primary" style="position: absolute;bottom: 0;width: 100%;" @click="toDraftList()">确定</button> -->
		</uni-popup>
	</view>

</template>

<script>
import appConfig from "/common/appConfig.js"
import {
	queryTableDataByCategory,
	confirmInstance,
	getProcSelects
} from "../asset/api/index.js"
export default {
	mixins: [appConfig],
	data() {
		return {
			tabs: [
				{ title: '全部应用', content: '第一页内容' }
			],
			activeIndex: 0,     // 当前选中索引
			itemWidth: 0,       // 单个 Tab 的宽度
			lineWidth: 0,
			scrollLeft: 0,     // 滚动条位置
			lineLeft: 0,
			editing: false,
			type: null,
			groupName: null,    // 分组名称参数
			procSelects: [],
			theme: false,
			// 当前弹窗URL
			currentPopUrl: '',
		}
	},
	watch: {
		theme(newVal) {
			uni.setStorageSync('theme', newVal);
			if (newVal) {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#2b2b2b', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#2b2b2b',
					color: '#ffffff',
					selectedColor: '#fff'
				});
			} else {
				uni.setNavigationBarColor({
					frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
					backgroundColor: '#ffffff', // 背景颜色
					// animation: { duration: 100 } // 过渡动画
				});
				uni.setTabBarStyle({
					backgroundColor: '#ffffff',
					color: '#000000',
					selectedColor: '#000'
				});
			}
		}
	},
	onLoad(data) {
		console.log('应用管理页面加载，参数:', data);

		// 保存类型参数和分组名称参数
		if (data.type) {
			this.type = data.type;
			console.log('设置页面类型:', this.type);
		}
		if (data.groupName) {
			this.groupName = decodeURIComponent(data.groupName);
			console.log('设置分组名称:', this.groupName);
		}

		// 根据权限控制是否显示各分类应用
		this.$nextTick(() => {
			// 确保权限已加载完成
			if (this.permissionsLoaded) {
				console.log('权限已加载完成，直接更新分类可见性');
				this.updateCategoryVisibility();
				// 权限加载完成后，根据type参数设置正确的activeIndex
				this.setActiveIndexByType(data.type);
			} else {
				console.log('权限尚未加载完成，等待权限加载');
				// 如果权限尚未加载完成，等待权限加载
				const checkPermissions = setInterval(() => {
					if (this.permissionsLoaded) {
						console.log('权限加载完成，更新分类可见性');
						this.updateCategoryVisibility();
						// 权限加载完成后，根据type参数设置正确的activeIndex
						this.setActiveIndexByType(data.type);
						clearInterval(checkPermissions);
					}
				}, 100);

				// 设置超时，避免无限等待
				setTimeout(() => {
					clearInterval(checkPermissions);
					console.log('权限加载超时，强制更新分类可见性');
					this.updateCategoryVisibility();
					// 即使超时，也尝试根据type参数设置正确的activeIndex
					this.setActiveIndexByType(data.type);
				}, 3000);
			}
		});
	},
	computed: {
		// 底部滑动条样式
		lineStyle() {
			return {
				width: `${this.lineWidth}px`, // 改用 lineWidth
				transform: `translateX(${this.activeIndex * this.itemWidth + (this.itemWidth - this.lineWidth) / 2}px)`, // 居中计算
				transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
				// #ifdef APP-PLUS
				width: '166rpx',
				transform: `translateX(${this.activeIndex * 166}rpx)`,
				marginLeft: '6rpx',
				margin: ' 0 auto',
				// #endif
			}
		},
		showCommonApps() {
			return !this.type;
		},
		showAssetApps() {
			// 暂时隐藏type参数控制逻辑，始终显示所有应用
			// let type = this.type;
			// return !type || type == "asset";
			return true;
		},
		showListApps() {
			// 暂时隐藏type参数控制逻辑，始终显示所有应用
			// let type = this.type;
			// return !type || type == "list";
			return true;
		},
		showMessageApps() {
			// 暂时隐藏type参数控制逻辑，始终显示所有应用
			// let type = this.type;
			// return !type || type == "message" || type == "posture";
			return true;
		},
		commonAppCount() {
			return this.commonApps.length;
		},
		// 最多添加6个app到常用列表
		availableToAddApp() {
			return this.commonAppCount < 6;
		},
		addAppIconStyle() {
			let style = {
				color: this.availableToAddApp ? "#3190F9" : "gray"
			};
			return style;
		}
	},
	onShow() {
		console.log('应用管理页面显示');
		this.theme = uni.getStorageSync('theme') || false;
		if (this.theme) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#2b2b2b', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#2b2b2b',
				color: '#ffffff',
				selectedColor: '#fff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#ffffff', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#ffffff',
				color: '#000000',
				selectedColor: '#000'
			});
		}

		// 更新分类可见性
		this.$nextTick(() => {
			// 如果权限已加载完成，直接更新分类可见性
			if (this.permissionsLoaded) {
				console.log('onShow - 权限已加载完成，更新分类可见性');
				this.updateCategoryVisibility();
				// 重新根据type参数设置activeIndex
				this.setActiveIndexByType(this.type);
			} else {
				console.log('onShow - 权限尚未加载完成，等待权限加载');
				// 如果权限尚未加载完成，等待权限加载
				const checkPermissions = setInterval(() => {
					if (this.permissionsLoaded) {
						console.log('onShow - 权限加载完成，更新分类可见性');
						this.updateCategoryVisibility();
						// 权限加载完成后，根据type参数设置正确的activeIndex
						this.setActiveIndexByType(this.type);
						clearInterval(checkPermissions);
					}
				}, 100);

				// 设置超时，避免无限等待
				setTimeout(() => {
					clearInterval(checkPermissions);
					console.log('onShow - 权限加载超时，强制更新分类可见性');
					this.updateCategoryVisibility();
					// 即使超时，也尝试根据type参数设置正确的activeIndex
					this.setActiveIndexByType(this.type);
				}, 3000);
			}
		});
	},
	mounted() {
		// 获取 Tab 项宽度（根据实际样式调整）
		this.calcTabWidth()
		this.theme = uni.getStorageSync('theme') || false;
		if (this.theme) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#2b2b2b', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#2b2b2b',
				color: '#ffffff',
				selectedColor: '#fff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#000000', // 文字颜色（仅支持 #000000 / #ffffff）
				backgroundColor: '#ffffff', // 背景颜色
				// animation: { duration: 100 } // 过渡动画
			});
			uni.setTabBarStyle({
				backgroundColor: '#ffffff',
				color: '#000000',
				selectedColor: '#000'
			});
		}
	},
	methods: {
		getProcSelects() {
			getProcSelects().then(res => {
				// 过滤掉value为2的告警工单选项
				this.procSelects = res.data.filter(item => item.value !== 2);
			})
		},
		closePop() {
			this.$refs.popup.close();
		},
		showPop(url) {
			// 保存URL，以便在选择流程后使用
			this.currentPopUrl = url;
			this.$refs.popup.open("bottom");
			this.getProcSelects();
		},
		createToDolist(item) {
			console.log(item);
			uni.navigateTo({
				url: `/pages/list/draft_list?procKey=templatea&procName=${item.label}&type=${item.value}`
			})
			this.closePop();
		},
		// 统一计算逻辑（跨平台）
		calcTabWidth() {
			// #ifdef APP-PLUS
			const dom = uni.requireNativePlugin('dom')
			console.log(this.$refs[`tabItem${this.activeIndex}`][0])
			dom.getComponentRect(this.$refs[`tabItem${this.activeIndex}`][0], res => {
				console.log('APP端元素尺寸:', res)
				if (res?.size?.width) {
					this.itemWidth = res.size.width
					this.lineWidth = res.size.width * 0.8
					// 测试用提示
					// uni.showToast({ title: `宽度:${this.itemWidth}`, icon: 'none' })
				} else {
					// uni.showToast({ title: '获取宽度失败', icon: 'none' })
				}
			})
			// #endif

			// #ifndef APP-PLUS
			// 非原生环境通用逻辑
			const query = uni.createSelectorQuery().in(this)
			query.select('.tab-item').boundingClientRect(res => {
				if (res) {
					this.itemWidth = res.width
					this.lineWidth = res.width * 0.8
				}
			}).exec()
			// #endif
		},

		// 调整滚动位置（跨平台兼容）
		adjustScrollPosition() {
			const systemInfo = uni.getSystemInfoSync();
			console.log(systemInfo)
			let offset = this.activeIndex * this.itemWidth - systemInfo.windowWidth / 2 + this.itemWidth / 2
			// #ifdef APP-PLUS
			const dom = uni.requireNativePlugin('dom');
			const el = this.$refs.tabLine
			dom.scrollToElement(el, {
				offset: 0
			})
			// #endif
			// #ifdef APP-PLUS
			// 原生环境增加安全偏移
			offset = Math.max(0, offset - 8)
			// 原生滚动控制
			this.$nextTick(() => {
				this.$refs.tabScroll.setScrollLeft({
					scrollLeft: offset,
					duration: 300
				})
			})
			// #else
			// 非原生环境直接赋值
			this.scrollLeft = Math.max(0, offset)
			// #endif

			// 滑动条位置计算（跨平台通用）
			this.lineLeft = this.activeIndex * this.itemWidth + (this.itemWidth - this.lineWidth) / 2
		},

		// 点击切换 Tab
		switchTab(index) {
			this.activeIndex = index
			this.adjustScrollPosition()
		},

		// 滑动切换回调
		onSwiperChange(e) {
			this.activeIndex = e.detail.current
			this.adjustScrollPosition()
		},


		// 重置常用app列表
		resetAppList() {
			this.commonAppNames.splice(0, this.commonAppNames.length, ...this.defaultCommonApps);
			this.storageCommonAppNames();
			this.editing = false;
		},
		// 移除常用列表中指定选项
		removeApp(index) {
			this.commonAppNames.splice(index, 1);
			this.storageCommonAppNames();
		},
		addApp(app) {
			if (this.availableToAddApp) {
				this.commonAppNames.push(app.name);
				this.storageCommonAppNames();
			}
		},

		/**
		 * 更新分类可见性
		 * 根据权限动态生成标签页
		 */
		updateCategoryVisibility() {
			console.log('更新分类可见性 - 开始');
			console.log('权限状态:', {
				availableGroups: this.availableGroups,
				type: this.type,
				groupName: this.groupName
			});

			// 动态生成标签页列表
			const newTabs = [{ title: '全部应用', content: '第一页内容' }];

			// 添加所有有权限的分组标签页
			this.availableGroups.forEach((group, index) => {
				newTabs.push({
					title: group.name,
					content: `第${index + 2}页内容`,
					groupData: group
				});
			});

			// 检查当前选中的标签页是否仍然有效
			const currentTabTitle = this.tabs[this.activeIndex]?.title;
			const newActiveIndex = newTabs.findIndex(tab => tab.title === currentTabTitle);

			// 更新标签页列表
			this.tabs = newTabs;
			console.log('更新后的标签页列表:', this.tabs);

			// 如果当前选中的标签页不存在，则自动切换到全部应用
			if (newActiveIndex === -1 || this.activeIndex >= this.tabs.length) {
				console.log('当前选中的标签页不存在，切换到全部应用');
				this.activeIndex = 0;
				this.adjustScrollPosition();
			} else if (newActiveIndex !== this.activeIndex) {
				// 如果标签页位置发生变化，更新activeIndex
				this.activeIndex = newActiveIndex;
				this.adjustScrollPosition();
			}

			console.log('更新分类可见性 - 完成');
		},

		/**
		 * 根据type参数和groupName参数设置正确的activeIndex
		 * @param {String} type 页面类型
		 */
		setActiveIndexByType(type) {
			console.log('根据type参数设置activeIndex, type:', type, 'groupName:', this.groupName);
			if (!type) {
				console.log('无type参数，保持activeIndex为默认值:', this.activeIndex);
				return;
			}

			// 获取当前标签页列表
			const tabs = this.tabs;
			console.log('当前标签页列表:', tabs);

			// 根据type参数或groupName参数查找对应的标签页索引
			let targetIndex = 0; // 默认为全部应用
			let targetTitle = '';

			// 优先使用groupName参数
			if (this.groupName) {
				targetTitle = this.groupName;
			} else {
				// 根据type参数确定目标标签页标题
				const typeToTitleMap = {
					'list': '工单管理',
					'asset': '告警管理',
					'posture': '态势呈现',
					'external': '外部应用'
				};
				targetTitle = typeToTitleMap[type];
			}

			if (targetTitle) {
				// 查找目标标签页的索引
				const index = tabs.findIndex(tab => tab.title === targetTitle);
				if (index !== -1) {
					targetIndex = index;
					console.log(`找到${targetTitle}标签页，索引为:`, targetIndex);
				} else {
					console.log(`未找到${targetTitle}标签页，使用默认索引`);
				}
			}

			// 设置activeIndex并调整滚动位置
			console.log('设置activeIndex为:', targetIndex);
			this.activeIndex = targetIndex;
			this.adjustScrollPosition();
		}
	}
}
</script>

<style lang="scss" scoped>
.app-manage {
	// padding: 10px 0;

	.page-group {
		margin: 10rpx 20rpx;
		background-color: white;
		border-radius: 10rpx;
		border: 2rpx solid #eee;
		filter: drop-shadow(4rpx 4rpx 3rpx rgba(107, 107, 107, 0.145));
	}

	.grid-item-box {
		flex: 1;
		// position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 25rpx 0 30rpx;

		.text {
			font-size: 24rpx;
			margin-top: 10rpx;
		}

		.tool-icon {
			top: 0px;
			right: -15px;
		}

		.external-label {
			position: absolute;
			top: -5px;
			right: -5px;
			background-color: #ff9800;
			color: white;
			font-size: 18rpx;
			padding: 2rpx 6rpx;
			border-radius: 8rpx;
			transform: scale(0.8);
		}
	}
}

.container {
	width: 100vw;
	height: 123vh;

	display: flex;
	flex-direction: column;
	overflow: hidden;

}

/* Tab 栏样式 */
.tab-scroll {
	width: 100%;
	height: 44px;
	background: #fff;
	border-bottom: 1px solid #eee;

}

.tab-bar {
	position: relative;
	height: 100%;
	white-space: nowrap;
}

.tab-item {
	display: inline-block;
	height: 44px;
	line-height: 44px;
	padding: 0 26rpx;
	font-size: 29rpx;
	color: #666;
	transition: color 0.3s;
}

.tab-item.active {
	color: #007AFF;
	font-weight: bold;
}

/* 底部滑动条 */
.tab-line {
	position: absolute;
	bottom: 0;
	height: 3px;
	background: #007AFF;
	transition: transform 0.3s ease;
}

/* 内容区域 */
swiper {
	flex: 1;
	height: calc(100vh - 44px);
}

.content {
	height: 100%;
	// padding: 20px;
	background: #fff;
}

/* 隐藏滚动条（全平台通用） */
// .tab-scroll {
// 	overflow: hidden !important;
// }

/* 针对 H5 的隐藏方式 */
.tab-scroll ::-webkit-scrollbar {
	display: none !important;
	width: 0 !important;
	height: 0 !important;
	color: transparent !important;
}

::v-deep .uni-easyinput-input {
	flex: 0;

	.uni-easyinput__content {
		border-radius: 6rpx;
	}

	.uni-easyinput__content-input {
		height: 53rpx;
	}
}


.popup {
	:deep(.uni-section__content-title) {
		font-weight: bold;
	}
}

.popup-content {
	max-height: 60vh;
	padding-bottom: 10px;
	position: relative;

	.process-type {
		background-color: #F7F8FA;
		height: 30pt;
		font-size: .8em;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 5px;

		&-selected {
			background-color: #3190F9;
			color: white;
		}

		// text {
		// 	overflow: hidden;
		// 	white-space: nowrap;
		// 	text-overflow: ellipsis;
		// }
	}
}




.container-dark {
	:deep(.tab-scroll) {
		.tab-bar {
			background: #2b2b2b;
		}

		.tab-item {
			color: #fff;
		}

	}

	:deep(.uni-scroll-view) {
		background-color: #2b2b2b !important;
	}

	:deep(.uni-section) {
		background-color: #2b2b2b !important;
		color: #fff !important;

		image {
			filter: invert(100%);
		}

		.uni-section__content-title {
			color: #fff !important;

		}
	}

	:deep(.uni-swiper-wrapper) {
		.content {
			background-color: #2b2b2b !important;
		}
	}
	:deep(.uniui-minus-filled){
		color: #ffffffa9 !important;
	}
	:deep(.uniui-close){
		color: #ffffffa9 !important;
	}
	:deep(.process-type){
		background-color: #6a6a6ab8 !important;
		color: #fff !important;
	}
}
</style>